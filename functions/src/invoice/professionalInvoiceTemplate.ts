interface InvoiceData {
  invoice: any;
  project: any;
  client: any;
  payments: any[];
}

interface CompanyDetails {
  name: string;
  tagline: string;
  address: string;
  phone: string;
  email: string;
  website: string;
  gstin: string;
}

export function generateProfessionalInvoiceHTML(data: InvoiceData): string {
  const { invoice, project, client, payments } = data;
  
  const companyDetails: CompanyDetails = {
    name: 'AGNEX STUDIO',
    tagline: 'Digital Excellence in Every Pixel',
    address: 'Telangana, India',
    phone: '+91 91888 78022',
    email: '<EMAIL>',
    website: 'agnex.in',
    gstin: '32COLPG6895J1ZE'
  };

  const formatDate = (timestamp: any): string => {
    if (!timestamp) return 'N/A';
    try {
      let date: Date;
      if (timestamp && typeof timestamp.toDate === 'function') {
        date = timestamp.toDate();
      } else if (timestamp && timestamp.seconds) {
        date = new Date(timestamp.seconds * 1000);
      } else {
        date = new Date(timestamp);
      }
      return date.toLocaleDateString('en-IN', {
        year: 'numeric',
        month: 'short',
        day: '2-digit'
      });
    } catch {
      return 'N/A';
    }
  };

  const formatCurrency = (amount: number, currency = 'INR'): string => {
    const locale = currency === 'INR' ? 'en-IN' : 'en-US';
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency
    }).format(amount);
  };

  const getStatusColor = (status: string): string => {
    switch (status.toLowerCase()) {
      case 'paid': return '#10b981';
      case 'sent': return '#3b82f6';
      case 'overdue': return '#ef4444';
      default: return '#6b7280';
    }
  };

  const totalAmount = payments.reduce((sum, payment) => sum + payment.amount, 0);

  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice ${invoice.invoiceNumber}</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #1f2937;
            background: #ffffff;
        }

        .invoice-container {
            width: 210mm;
            min-height: 297mm;
            margin: 0 auto;
            padding: 0;
            background: white;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            position: relative;
        }

        /* Header Section */
        .header {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            color: white;
            padding: 40px;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            transform: rotate(45deg);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            position: relative;
            z-index: 1;
        }

        .company-info {
            flex: 1;
        }

        .company-name {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 8px;
            letter-spacing: -0.5px;
        }

        .company-tagline {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 20px;
            font-weight: 300;
        }

        .company-details {
            font-size: 13px;
            line-height: 1.8;
            opacity: 0.95;
        }

        .invoice-title {
            text-align: right;
            flex: 0 0 auto;
        }

        .invoice-title h1 {
            font-size: 42px;
            font-weight: 700;
            margin-bottom: 8px;
            letter-spacing: -1px;
        }

        .invoice-number {
            font-size: 18px;
            font-weight: 500;
            opacity: 0.9;
        }

        /* Invoice Details Section */
        .invoice-details {
            padding: 40px;
            background: #f8fafc;
            border-bottom: 1px solid #e2e8f0;
        }

        .details-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .detail-card {
            background: white;
            padding: 24px;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .detail-card h3 {
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            color: #6b7280;
            margin-bottom: 12px;
            letter-spacing: 0.5px;
        }

        .detail-card .value {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 4px;
        }

        .detail-card .sub-value {
            font-size: 14px;
            color: #6b7280;
        }

        .status-badge {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            background: ${getStatusColor(invoice.status)};
            color: white;
        }

        /* Client Information */
        .client-info {
            background: white;
            padding: 40px;
            border-bottom: 1px solid #e2e8f0;
        }

        .client-info h2 {
            font-size: 16px;
            font-weight: 600;
            color: #2563eb;
            margin-bottom: 20px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .client-details {
            background: #f8fafc;
            padding: 24px;
            border-radius: 12px;
            border-left: 4px solid #2563eb;
        }

        .client-name {
            font-size: 20px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .client-contact {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.6;
        }

        /* Payment Items Table */
        .payment-items {
            padding: 40px;
        }

        .payment-items h2 {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 24px;
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .items-table thead {
            background: #2563eb;
            color: white;
        }

        .items-table th {
            padding: 16px;
            text-align: left;
            font-weight: 600;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .items-table td {
            padding: 16px;
            border-bottom: 1px solid #e2e8f0;
            font-size: 14px;
        }

        .items-table tbody tr:hover {
            background: #f8fafc;
        }

        .items-table tbody tr:last-child td {
            border-bottom: none;
        }

        .amount-cell {
            font-weight: 600;
            color: #1f2937;
        }

        .date-cell {
            color: #6b7280;
        }

        /* Totals Section */
        .totals-section {
            background: #f8fafc;
            padding: 40px;
            border-top: 1px solid #e2e8f0;
        }

        .totals-container {
            max-width: 400px;
            margin-left: auto;
        }

        .total-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #e2e8f0;
        }

        .total-row:last-child {
            border-bottom: none;
            background: #2563eb;
            color: white;
            padding: 20px 24px;
            margin-top: 16px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 18px;
        }

        .total-label {
            font-size: 14px;
            color: #6b7280;
        }

        .total-value {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
        }

        .total-row:last-child .total-label,
        .total-row:last-child .total-value {
            color: white;
        }

        /* Notes Section */
        .notes-section {
            padding: 40px;
            background: white;
        }

        .notes-section h3 {
            font-size: 16px;
            font-weight: 600;
            color: #2563eb;
            margin-bottom: 16px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .notes-content {
            background: #f8fafc;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #2563eb;
            font-size: 14px;
            line-height: 1.6;
            color: #4b5563;
        }

        /* Footer */
        .footer {
            background: #1f2937;
            color: white;
            padding: 40px;
            text-align: center;
        }

        .footer-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-bottom: 24px;
        }

        .footer-section h4 {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 12px;
            color: #e5e7eb;
        }

        .footer-section p {
            font-size: 12px;
            line-height: 1.6;
            color: #9ca3af;
        }

        .footer-bottom {
            padding-top: 24px;
            border-top: 1px solid #374151;
            font-size: 12px;
            color: #9ca3af;
        }

        .footer-brand {
            font-weight: 600;
            color: #2563eb;
        }

        /* Print Styles */
        @media print {
            .invoice-container {
                box-shadow: none;
                margin: 0;
                width: 100%;
                min-height: 100vh;
            }
            
            body {
                background: white;
            }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .invoice-container {
                width: 100%;
                min-height: 100vh;
            }
            
            .header-content {
                flex-direction: column;
                gap: 20px;
            }
            
            .invoice-title {
                text-align: left;
            }
            
            .details-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .footer-content {
                grid-template-columns: 1fr;
                gap: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <!-- Header Section -->
        <div class="header">
            <div class="header-content">
                <div class="company-info">
                    <div class="company-name">${companyDetails.name}</div>
                    <div class="company-tagline">${companyDetails.tagline}</div>
                    <div class="company-details">
                        <div>${companyDetails.address}</div>
                        <div>${companyDetails.phone} • ${companyDetails.email}</div>
                        <div>${companyDetails.website}</div>
                        <div>GSTIN: ${companyDetails.gstin}</div>
                    </div>
                </div>
                <div class="invoice-title">
                    <h1>INVOICE</h1>
                    <div class="invoice-number">#${invoice.invoiceNumber}</div>
                </div>
            </div>
        </div>

        <!-- Invoice Details Section -->
        <div class="invoice-details">
            <div class="details-grid">
                <div class="detail-card">
                    <h3>Issue Date</h3>
                    <div class="value">${formatDate(invoice.issueDate)}</div>
                </div>
                <div class="detail-card">
                    <h3>Due Date</h3>
                    <div class="value">${formatDate(invoice.dueDate)}</div>
                </div>
                <div class="detail-card">
                    <h3>Status</h3>
                    <div class="status-badge">${invoice.status}</div>
                </div>
            </div>
            
            <div class="details-grid">
                <div class="detail-card">
                    <h3>Project</h3>
                    <div class="value">${project.name || 'N/A'}</div>
                    <div class="sub-value">${invoice.type === 'individual' ? 'Individual Payment' : 'Consolidated Invoice'}</div>
                </div>
                <div class="detail-card">
                    <h3>Currency</h3>
                    <div class="value">${invoice.currency}</div>
                </div>
                <div class="detail-card">
                    <h3>Payment Count</h3>
                    <div class="value">${payments.length}</div>
                    <div class="sub-value">${payments.length === 1 ? 'payment' : 'payments'}</div>
                </div>
            </div>
        </div>

        <!-- Client Information -->
        <div class="client-info">
            <h2>Bill To</h2>
            <div class="client-details">
                <div class="client-name">${client.name || client.displayName || 'Client Name'}</div>
                <div class="client-contact">
                    <div>${client.email || '<EMAIL>'}</div>
                    ${client.phone ? `<div>${client.phone}</div>` : ''}
                </div>
            </div>
        </div>

        <!-- Payment Items -->
        <div class="payment-items">
            <h2>Payment Details</h2>
            <table class="items-table">
                <thead>
                    <tr>
                        <th>Description</th>
                        <th>Payment Date</th>
                        <th>Type</th>
                        <th style="text-align: right;">Amount</th>
                    </tr>
                </thead>
                <tbody>
                    ${payments.map((payment, index) => `
                        <tr>
                            <td>
                                <div style="font-weight: 500;">
                                    ${payment.description || `Payment #${index + 1}`}
                                </div>
                                ${payment.linkedTicketId ? `<div style="font-size: 12px; color: #6b7280;">Ticket: ${payment.linkedTicketId}</div>` : ''}
                            </td>
                            <td class="date-cell">${formatDate(payment.paidAt || payment.createdAt)}</td>
                            <td>
                                <span style="background: ${payment.paymentType === 'change_request' ? '#f59e0b' : '#3b82f6'}; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;">
                                    ${payment.paymentType === 'change_request' ? 'Change Request' : 'Regular'}
                                </span>
                            </td>
                            <td class="amount-cell" style="text-align: right;">${formatCurrency(payment.amount, payment.currency)}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>

        <!-- Totals Section -->
        <div class="totals-section">
            <div class="totals-container">
                <div class="total-row">
                    <span class="total-label">Subtotal</span>
                    <span class="total-value">${formatCurrency(totalAmount, invoice.currency)}</span>
                </div>
                <div class="total-row">
                    <span class="total-label">TOTAL AMOUNT</span>
                    <span class="total-value">${formatCurrency(invoice.totalAmount, invoice.currency)}</span>
                </div>
            </div>
        </div>

        <!-- Notes Section -->
        ${invoice.notes ? `
        <div class="notes-section">
            <h3>Notes</h3>
            <div class="notes-content">
                ${invoice.notes}
            </div>
        </div>
        ` : ''}

        <!-- Footer -->
        <div class="footer">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>Terms & Conditions</h4>
                    <p>
                        ${invoice.terms || 'Payment is due within 30 days of invoice date. Late payments may incur additional charges. This invoice is digitally generated and does not require a physical signature.'}
                    </p>
                </div>
                <div class="footer-section">
                    <h4>Thank You</h4>
                    <p>
                        Thank you for choosing Agnex Studio for your project needs. We appreciate your business and look forward to working with you again.
                    </p>
                </div>
            </div>
            <div class="footer-bottom">
                <div>Generated on ${new Date().toLocaleDateString('en-IN')} at ${new Date().toLocaleTimeString('en-IN')}</div>
                <div>This is a digitally generated invoice by <span class="footer-brand">AGNEX STUDIO</span></div>
            </div>
        </div>
    </div>
</body>
</html>
  `;
} 